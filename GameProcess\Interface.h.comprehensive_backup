#ifndef _INTERFACE_H
#define _INTERFACE_H

//유료 아이템 정보
typedef struct _tagTExpireAbilitys
{
	INT		nItemExExpireTime;			//추가 가방 万료时间

	INT		nSavedItemExExpireTime;		//추가 窗口고 万료时间
	
	FLOAT	fExpRate;					//经验值 추가 비율
	INT		nExpRateExpireTime;			//经验值 추가 비율 万료时间
	
	FLOAT	fDropRate;					//드랍율 추가 비율
	INT		nDropRateExpireTime;		//드랍율 추가 비율 万료时间
	
	FLOAT	fMiningRate;				//채광률 추가 비율
	INT		nMiningRateExpireTime;		//채광률 추가 비율 万료时间

	INT		nIgnoreLevelExpireTime;		//怪物 等级 무小时 万료时间
	
	INT		nUserTitleExprieTime;		//用户 타이틀 万료时间

	enum { NUM_ITEMS = 7 };

} TEXPIREABILITYS, *LPTEXPIREABILITYS;

class CInterface
{ 
public:
	 CInterface();
	~CInterface();
public:
//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~//
//좌측상단的 Message 관리。
//---------------------------------------------------------------------------------------------------------------//
	ClientSysMsg	m_xClientSysMsg;
//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~//

//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~//
//Main Interface 处理변수。
//---------------------------------------------------------------------------------------------------------------//
	CWHWilImageData*	m_pxInterImg;					//게임상从/在 사용할 인터페이스 已经지。
//CWHWilImageData m_xInterImgEx;// 인터페이스 관련 已经지。

	COMMONITEMSET		m_stCommonItem;					//공용 아이템 保存(마우스 아이템)。
	RECT				m_rcMain;						//메인인터페이스 전체 区域。

	RECT				m_rcHPS;						
	RECT				m_rcMPS;						
	RECT				m_rcExpS;						
	RECT				m_rcWetS;						

	RECT				m_rcChatLine[_MAX_CHATLINE];	//5줄的 채팅区域。
	RECT				m_rcLargeChatLine[_MAX_CHATLINE];	//14줄的 채팅区域。

	RECT				m_rcChat;						//채팅窗口 区域。
	RECT				m_rcLargeChat;					//커다란 채팅窗口 区域。
	INT					m_nFstLine;						//채팅화면라인的 제日 윗줄이 가리키는 채팅리스트的 번호인덱스。
	INT					m_nFstLineInLarge;
	CList<CHAT>			m_xChatlist;					//或누어진 채팅 데이타。
	CList<CHAT>			m_xLargeChatlist;				//或누어진 채팅 데이타 - 큰 채팅窗口용。


	INT					m_nFstLineInSystemChat;
	RECT				m_rcSystemChat;
	RECT				m_rcSystemChatLine[_SYSTEM_CHATLINE];	//4줄的 채팅区域。
	CList<CHAT>			m_xSystemChat;

	INT					m_nFstLineInNormalChat;
	RECT				m_rcNormalChat;
	RECT				m_rcNormalChatLine[_SYSTEM_CHATLINE];	//4줄的 채팅区域。

	CList<CHAT>			m_xNormalChat;

	RECT				m_rcLargeChatRgn;				//메인인터페이스 전체 区域。


	CDLList<CHAT>		m_xChat;						//或누어진 채팅 데이타。

	CDLList<INT>		m_xWndOrderList;				
	BOOL				m_bMoveFocusedWnd;

	//벨트관련。
	SHORT				m_shBeltHeight;
	BYTE				m_bBeltState;
	RECT				m_rcBeltCell[_BELT_MAX_CELL];
	ITEMSET				m_stBeltItem[_MAX_BELT_ITEM];

	CGameBtn			m_xInterBtn[_NEW_MAX_INTER_BTN];	//按钮。
	CGameBtn			m_xInterBtnofSiege;					//발석거按钮。
	CGameBtn			m_xInterBtnofNewMessage;			//새메세지按钮。
	CGameBtn			m_xInterBtnFaceImgUp;				//已经지 업用/向드 按钮。


	CScrlBar			m_xScrlBarInSmallChat;
	CScrlBar			m_xScrlBarInLargeChat;

	BOOL				m_bShowKey;
	BOOL				m_bNewFliker;
	DWORD				m_dwNewFliker;

	INT					m_nIdxMsgBox;					

	DWORD				m_dwViewModeClickTick;				//화면좌우측(가방，장착) 移动 가능여부체크틱。

	DWORD				m_dwQueryMsgTime;
	CMiniMap			m_xMiniMap;
	CMiniMapInSiege		m_xMiniMapInSiege;
	BOOL				m_bViewMiniMap;

	BOOL				m_bViewMiniMapInMain;

	BOOL				m_bIsLargeChat;
	INT					m_nChatMode;

	INT					m_nInformationMode;

	CWHSurface			m_hbmUserFace;
	BOOL				m_bLoadUserFace;
	BOOL				m_bIsThereUserFile;
	DWORD				m_dwFaceImgTimer;

	RECT				m_rcInfoModeChange;
	RECT				m_rcCharFace;
	RECT				m_rcLogo;
	RECT				m_rcAcnum;
	RECT				m_rcDcnum;

	POINT				m_ptFrameLoc;
	BYTE				m_bDcScMcShow;

	BOOL				m_bIsMoving;
	BOOL				m_bIsDown;

	BOOL				m_bAcShow;
	
	BOOL				m_bChatallDeny;
	BOOL				m_bDenyGuild;
	BOOL				m_bDenyShout;

	BOOL				m_bReadyViewMap;

	BOOL				m_bReadyInforMap;

	BOOL				m_bHasSiege;
	BOOL				m_bHasNewMessage;

	BOOL				m_bShowUserPic;

	DWORD				m_dwSaveItemTimer;

	RECT				m_rcWarning1;
	RECT				m_rcWarning2;

	BOOL				m_bMoveWarning;
	INT					m_nMoveOffset;
	INT					m_nWarningCnt;
	SIZE				m_sizeWarning;

	CHAR				m_szWarning[MAX_PATH];
	CHAR				m_szWarningOrigin[MAX_PATH];
	DWORD				m_dwWarningTimer;

	DWORD				m_dwLogoTimer;
	INT					m_nLogoIdx;
//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~//


//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~//
//귓말 리스트 관련，保存 매크用/向관련。
//---------------------------------------------------------------------------------------------------------------//
	CList<CHAT>			m_xHearList;
	INT					m_nSelHearNum;
	LONG 				CtrlNumPushed(WPARAM wParam, LPARAM lParam);
	VOID				SetHearList();
	LPCHAT				FindHearList(CHAR* pszHear);
	VOID				AddHearList(CHAR* pszHear);
	VOID				LoadHearList();					//해당西버，해당 캐릭。
	VOID				SaveHearList();					//해당西버，해당 캐릭。
	VOID				ShowHearList();

	BYTE				*m_temp;

	CList<CHAT>			m_xMacroList;
	INT					m_nSelMacroNum;
	LONG 				AltNumPushed(WPARAM wParam, LPARAM lParam);
	VOID				SetMacroList();
	LPCHAT				FindMacroList(CHAR* pszMacro);
	VOID				AddMacroList(CHAR* pszMacro);
	VOID				LoadMacroList();				//해당西버，해당 캐릭。
	VOID				SaveMacroList();				//해당西버，해당 캐릭。
	VOID				ShowMacroList();
//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~//


//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~//
//Main Interface 从/在 관리하는 Window。
//---------------------------------------------------------------------------------------------------------------//
	CInventoryWnd		m_xInventoryWnd;				//가방윈刀우。
	CStatusWnd			m_xStatusWnd;					//角色 정보 보기윈刀우(3개)。
	CStoreWnd			m_xStoreWnd;					//商店윈刀우。
	CExchangeWnd		m_xExchangeWnd;					//교환윈刀우。
	CGuildWnd			m_xGuildWnd;					//문원윈刀우。
	CGroupWnd			m_xGroupWnd;					//그룹윈刀우。
	CUserStateWnd		m_xUserStateWnd;				//그룹输入윈刀우。
	CChatPopWnd			m_xChatPopWnd;					//그룹输入윈刀우。
	CNPCWnd				m_xNPCWnd;						//NPC 대화窗口。
	CQuestWnd			m_xQuestWnd;					//퀘스트窗口。
	COptionWnd			m_xOptionWnd;					//옵션窗口。
	CHorseWnd			m_xHorseWnd;					//말窗口。
	CMagicWnd			m_xMagicWnd;					//마법窗口。
	CNoticEditWnd		m_xNoticeEditWnd;				//문파공지窗口。
	CBeltWnd			m_xBeltWnd;
	CSiegeWnd			m_xSiegeWnd;					//발석거윈刀우
	CMarketWnd			m_xMarketWnd;					//用户 아이템 交易 윈刀우
	CMarketUpWnd		m_xMarketUpWnd;					//用户 아이템 등록 윈刀우
	CMessengerWnd		m_xMessengerWnd;				//메신저
	CFaceImgUpWnd		m_xFaceImgUpWnd;				//사진등록
	CUtilWnd			m_xUtilWnd;						//유틸
	CViewMiniMapWnd		m_xViewMiniMapWnd;				//미니地图 뷰어
	CChatWnd			m_xChatWnd;						//채팅窗口
	CSettingWnd			m_xSettingWnd;					//환경设置窗口
	CMagicShortcutWnd	m_xMagicShortcutWnd;			//무공단축키窗口
	CMarketExWnd		m_xMarketExWnd;					//원보商店窗口
	CInventoryExWnd		m_xInventoryExWnd;				//추가 가방


	CGameOver			m_xGameOverWnd;					//게임종료。

	CHAR				m_pszLastCapture[MAX_PATH];

	INT					m_nTopWndID;
//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~//

public:
//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~//
//秒기화，删除，생성。
//---------------------------------------------------------------------------------------------------------------//
	VOID			Init();
	VOID			Destroy();
	VOID			CreateInterface(CImageHandler* pxImgHandler);
//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~//

//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~//
//채팅메小时지관련 함수。
//---------------------------------------------------------------------------------------------------------------//
	VOID			MsgAdd(DWORD dwFontColor, DWORD dwFontBackColor, CHAR* pszMsg = NULL);
	BOOL			MsgDelete();					//메小时지 删除。
//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~//

//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~//
//메인인터페이스관련 함수。
//---------------------------------------------------------------------------------------------------------------//
	VOID			RenderInterface(INT nLoopTime, POINT ptMousePos);	//화면在 인터페이스 관련부分钟을 보여준다。

	VOID			SetCaputeString(CHAR* pszChat);

	VOID			ShowWndList();
	VOID			ShowChatList();
	VOID			ShowLargeChatList();
	VOID			ShowBeltItem();
	VOID			ShowGameStatus();
	VOID			ShowMagicKey();

	VOID			ShowSystemChat();
	VOID			ShowNormalChat();
//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~//

//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~//
//벨트관련。
//---------------------------------------------------------------------------------------------------------------//
	BOOL				CheckItemPosOLD(CHAR* szName);
	BOOL				CheckItemPos(CHAR* szName);
	BOOL				SaveItemPos(CHAR* szName);

	VOID				DeleteBeltItem(INT nBeltInventoryNum);
	VOID				DeleteAllBeltItem();
	BOOL				AddBeltItem(CItem xItem, INT nBeltNum, BOOL bUseBeltNum = TRUE);
	INT					GetBeltNum(POINT ptMouse);
	BOOL				ExtractBeltItem(CItem* pxItem, INT nBeltInventoryNum);
	VOID				ChangeBeltWithCommonOLD(INT nBeltInventoryNum, LPCOMMONITEMSET pstCommonItemSet);
	VOID				ChangeBeltWithCommon(INT nBeltInventoryNum, LPCOMMONITEMSET pstCommonItemSet);
	VOID				SetCommonFromBelt(INT nBeltInventoryNum, LPCOMMONITEMSET pstCommonItemSet);
	VOID				SetBeltFromCommonOLD(INT nBeltInventoryNum, LPCOMMONITEMSET pstCommonItemSet);
	VOID				SetBeltFromCommon(INT nBeltInventoryNum, LPCOMMONITEMSET pstCommonItemSet);
	BOOL				AddNewBeltItem(LPCOMMONITEMSET pstCommonItemSet);

	int					FindSameItemInBelt(CItem xItem);
//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~//

//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~//
//윈刀우 리스트 관리 및 상태치 设置함수。
//---------------------------------------------------------------------------------------------------------------//
	INT				GetWindowInMousePos(POINT ptMouse);
	VOID			AddWindowToList(INT nID);
	VOID			DeleteWindowToList(INT nID);

	BOOL			WindowActivate(INT nID);
	VOID			WindowFocusChangedAndSetReadyMove(INT nID, POINT ptMouse);
	VOID			MoveTopWindow(POINT ptMouse);
	VOID			AllWindowsFocusOff();
	BOOL			IsTopWindow(INT nID);
//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~//

	VOID			SetViewMode();
	BOOL			CanViewModeChange();

	BOOL			UpdateUserPicInfo(CHAR *szName, INT nFaceImg);
	BOOL			LoadUserPic(CHAR *szName, INT nDate);
	VOID			DrawJobPic(BYTE bJob);

	BOOL			LoadFACFile(char *szFileName, CWHSurface *m_hbmFaceImg);

//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~//
// Message Funtion.
//---------------------------------------------------------------------------------------------------------------//
	BOOL			OnSysKeyDown(WPARAM wParam, LPARAM lParam);
	BOOL 			OnKeyDown(WPARAM wParam, LPARAM lParam);
	BOOL 			OnKeyUp(WPARAM wParam, LPARAM lParam);
	BOOL			OnLButtonDown(POINT ptMouse);
	BOOL			OnRButtonDown(POINT ptMouse);
	BOOL			OnLButtonUp(POINT ptMouse);
	BOOL			OnMouseMove(POINT ptMouse);
	BOOL			OnScrollDown();
	BOOL			OnScrollUp();
	BOOL			OnLButtonDoubleClick(POINT ptMouse);
	BOOL			OnMsgInputted(WPARAM wParam, LPARAM lParam);
	LRESULT			OnMsgBoxReturn(WPARAM wParam, LPARAM lParam);
//~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~//

	VOID			ItemClickSound(CItem* pxItem);
	VOID			ItemUseSound(CItem* pxItem);
	VOID			ChatListClear();
	VOID			ChangeChatSize();

	VOID			ShowGroupMember();

//유료 아이템 정보
public:
	VOID			SetExpireAbilitys();
	VOID			GetExpireTime( INT nExpireTime, INT& nYear, INT& nMonth, INT& nDay, INT& nHour, INT& nMin );
	VOID			ShowExpireAbilitys();

	BOOL			m_bDrawExpireAbilitys;
	
	INT				m_nExpireCnt;
	ITEMSTATE		m_TExpireState[ TEXPIREABILITYS::NUM_ITEMS ];	
	TEXPIREABILITYS	m_TExpireAbilitys;
};



#endif // _INTERFACE_H



