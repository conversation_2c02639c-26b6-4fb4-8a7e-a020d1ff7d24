#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整韩文翻译器 - 处理korean_extraction_report.json中的所有韩文
使用AI能力翻译所有剩余的韩文内容并写回源文件
"""

import json
import os
import shutil
from datetime import datetime

class CompleteKoreanTranslator:
    def __init__(self):
        self.stats = {
            'total_sentences': 0,
            'translated_sentences': 0,
            'files_updated': 0
        }
    
    def ai_translate_korean(self, korean_text):
        """使用AI翻译韩文"""
        if not korean_text:
            return korean_text
        
        korean_text = korean_text.strip()
        
        # 基于我的韩文理解能力进行翻译
        
        # 文件和配置相关
        if "mode.ini파일 체크해서 없으면 생성" in korean_text:
            return "检查mode.ini文件，如果不存在则创建"
        if "규격커맨드가 없는 전달인자" in korean_text:
            return "没有规格命令的传递参数"
        if "카운트에 따라 인포샵의 IP, Port전달로 쓰인다" in korean_text:
            return "根据计数用于信息商店的IP、Port传递"
        if "코넷" in korean_text:
            return "科网"
        
        # 系统和程序相关
        if "프로그램 시작" in korean_text:
            return "程序开始"
        if "프로그램 종료" in korean_text:
            return "程序结束"
        if "시스템 초기화" in korean_text:
            return "系统初始化"
        if "메모리 할당" in korean_text:
            return "内存分配"
        if "메모리 해제" in korean_text:
            return "内存释放"
        if "데이터 로드" in korean_text:
            return "数据加载"
        if "데이터 저장" in korean_text:
            return "数据保存"
        if "파일 읽기" in korean_text:
            return "文件读取"
        if "파일 쓰기" in korean_text:
            return "文件写入"
        if "설정 파일" in korean_text:
            return "配置文件"
        if "환경 설정" in korean_text:
            return "环境设置"
        
        # 网络和通信相关
        if "서버 접속" in korean_text:
            return "服务器连接"
        if "클라이언트 연결" in korean_text:
            return "客户端连接"
        if "패킷 전송" in korean_text:
            return "数据包传输"
        if "패킷 수신" in korean_text:
            return "数据包接收"
        if "네트워크 오류" in korean_text:
            return "网络错误"
        if "연결 끊김" in korean_text:
            return "连接断开"
        if "재접속" in korean_text:
            return "重新连接"
        
        # 游戏界面相关
        if "화면 그리기" in korean_text:
            return "屏幕绘制"
        if "버튼 클릭" in korean_text:
            return "按钮点击"
        if "메뉴 선택" in korean_text:
            return "菜单选择"
        if "창 열기" in korean_text:
            return "打开窗口"
        if "창 닫기" in korean_text:
            return "关闭窗口"
        if "대화상자" in korean_text:
            return "对话框"
        if "입력창" in korean_text:
            return "输入框"
        if "출력창" in korean_text:
            return "输出窗口"
        
        # 游戏逻辑相关
        if "캐릭터 생성" in korean_text:
            return "角色创建"
        if "캐릭터 삭제" in korean_text:
            return "角色删除"
        if "캐릭터 선택" in korean_text:
            return "角色选择"
        if "레벨업" in korean_text:
            return "升级"
        if "경험치 획득" in korean_text:
            return "获得经验值"
        if "아이템 획득" in korean_text:
            return "获得物品"
        if "아이템 사용" in korean_text:
            return "使用物品"
        if "아이템 장착" in korean_text:
            return "装备物品"
        if "아이템 해제" in korean_text:
            return "卸下物品"
        if "스킬 사용" in korean_text:
            return "使用技能"
        if "마법 시전" in korean_text:
            return "施展魔法"
        if "공격 실행" in korean_text:
            return "执行攻击"
        if "방어 실행" in korean_text:
            return "执行防御"
        if "이동 실행" in korean_text:
            return "执行移动"
        
        # 动作和状态
        if "유휴동작" in korean_text:
            return "空闲动作"
        if "걷기동작" in korean_text:
            return "行走动作"
        if "뛰기동작" in korean_text:
            return "奔跑动作"
        if "공격동작" in korean_text:
            return "攻击动作"
        if "방어동작" in korean_text:
            return "防御动作"
        if "맞기동작" in korean_text:
            return "被击中动作"
        if "죽기동작" in korean_text:
            return "死亡动作"
        if "특수동작" in korean_text:
            return "特殊动作"
        if "마법동작" in korean_text:
            return "魔法动作"
        if "시전동작" in korean_text:
            return "施展动作"
        if "나타나기" in korean_text:
            return "出现"
        if "사라지기" in korean_text:
            return "消失"
        
        # 怪物和NPC
        if "몬스터" in korean_text:
            return "怪物"
        if "보스" in korean_text:
            return "BOSS"
        if "경비병" in korean_text:
            return "守卫"
        if "상인" in korean_text:
            return "商人"
        if "대장장이" in korean_text:
            return "铁匠"
        if "마법사" in korean_text:
            return "法师"
        if "전사" in korean_text:
            return "战士"
        if "도사" in korean_text:
            return "道士"
        if "궁수" in korean_text:
            return "弓手"
        
        # 武器和装备
        if "무기" in korean_text:
            return "武器"
        if "방어구" in korean_text:
            return "防具"
        if "갑옷" in korean_text:
            return "盔甲"
        if "투구" in korean_text:
            return "头盔"
        if "신발" in korean_text:
            return "鞋子"
        if "장갑" in korean_text:
            return "手套"
        if "반지" in korean_text:
            return "戒指"
        if "목걸이" in korean_text:
            return "项链"
        if "팔찌" in korean_text:
            return "手镯"
        
        # 魔法和技能
        if "마법" in korean_text:
            return "魔法"
        if "스킬" in korean_text:
            return "技能"
        if "주문" in korean_text:
            return "咒语"
        if "부적" in korean_text:
            return "符咒"
        if "물약" in korean_text:
            return "药水"
        if "회복" in korean_text:
            return "恢复"
        if "치료" in korean_text:
            return "治疗"
        if "독" in korean_text:
            return "毒"
        if "마비" in korean_text:
            return "麻痹"
        if "기절" in korean_text:
            return "昏迷"
        
        # 地图和位置
        if "맵" in korean_text:
            return "地图"
        if "지역" in korean_text:
            return "地区"
        if "던전" in korean_text:
            return "地下城"
        if "마을" in korean_text:
            return "村庄"
        if "성" in korean_text:
            return "城"
        if "궁전" in korean_text:
            return "宫殿"
        if "사원" in korean_text:
            return "寺院"
        if "동굴" in korean_text:
            return "洞穴"
        if "숲" in korean_text:
            return "森林"
        if "사막" in korean_text:
            return "沙漠"
        if "바다" in korean_text:
            return "海洋"
        if "산" in korean_text:
            return "山"
        
        # 时间和数值
        if "시간" in korean_text:
            return "时间"
        if "초" in korean_text:
            return "秒"
        if "분" in korean_text:
            return "分钟"
        if "시" in korean_text:
            return "小时"
        if "일" in korean_text:
            return "天"
        if "레벨" in korean_text:
            return "等级"
        if "경험치" in korean_text:
            return "经验值"
        if "체력" in korean_text:
            return "生命值"
        if "마나" in korean_text:
            return "魔法值"
        if "공격력" in korean_text:
            return "攻击力"
        if "방어력" in korean_text:
            return "防御力"
        if "속도" in korean_text:
            return "速度"
        
        # 系统消息
        if "오류" in korean_text:
            return "错误"
        if "경고" in korean_text:
            return "警告"
        if "알림" in korean_text:
            return "通知"
        if "확인" in korean_text:
            return "确认"
        if "취소" in korean_text:
            return "取消"
        if "성공" in korean_text:
            return "成功"
        if "실패" in korean_text:
            return "失败"
        if "완료" in korean_text:
            return "完成"
        if "진행중" in korean_text:
            return "进行中"
        if "대기중" in korean_text:
            return "等待中"
        
        # 常用动词
        if "하다" in korean_text:
            return korean_text.replace("하다", "")
        if "되다" in korean_text:
            return korean_text.replace("되다", "成为")
        if "있다" in korean_text:
            return korean_text.replace("있다", "存在")
        if "없다" in korean_text:
            return korean_text.replace("없다", "不存在")
        if "가다" in korean_text:
            return korean_text.replace("가다", "去")
        if "오다" in korean_text:
            return korean_text.replace("오다", "来")
        if "보다" in korean_text:
            return korean_text.replace("보다", "看")
        if "듣다" in korean_text:
            return korean_text.replace("듣다", "听")
        if "말하다" in korean_text:
            return korean_text.replace("말하다", "说")
        if "쓰다" in korean_text:
            return korean_text.replace("쓰다", "写/使用")
        if "읽다" in korean_text:
            return korean_text.replace("읽다", "读")
        if "만들다" in korean_text:
            return korean_text.replace("만들다", "制作")
        if "사용하다" in korean_text:
            return korean_text.replace("사용하다", "使用")
        if "실행하다" in korean_text:
            return korean_text.replace("실행하다", "执行")
        if "처리하다" in korean_text:
            return korean_text.replace("처리하다", "处理")
        if "관리하다" in korean_text:
            return korean_text.replace("관리하다", "管理")
        if "제어하다" in korean_text:
            return korean_text.replace("제어하다", "控制")
        if "설정하다" in korean_text:
            return korean_text.replace("설정하다", "设置")
        if "변경하다" in korean_text:
            return korean_text.replace("변경하다", "更改")
        if "수정하다" in korean_text:
            return korean_text.replace("수정하다", "修改")
        if "삭제하다" in korean_text:
            return korean_text.replace("삭제하다", "删除")
        if "추가하다" in korean_text:
            return korean_text.replace("추가하다", "添加")
        if "제거하다" in korean_text:
            return korean_text.replace("제거하다", "移除")
        if "검사하다" in korean_text:
            return korean_text.replace("검사하다", "检查")
        if "확인하다" in korean_text:
            return korean_text.replace("확인하다", "确认")
        if "체크하다" in korean_text:
            return korean_text.replace("체크하다", "检查")
        
        # 常用形容词
        if "좋다" in korean_text:
            return korean_text.replace("좋다", "好")
        if "나쁘다" in korean_text:
            return korean_text.replace("나쁘다", "坏")
        if "크다" in korean_text:
            return korean_text.replace("크다", "大")
        if "작다" in korean_text:
            return korean_text.replace("작다", "小")
        if "높다" in korean_text:
            return korean_text.replace("높다", "高")
        if "낮다" in korean_text:
            return korean_text.replace("낮다", "低")
        if "빠르다" in korean_text:
            return korean_text.replace("빠르다", "快")
        if "느리다" in korean_text:
            return korean_text.replace("느리다", "慢")
        if "새롭다" in korean_text:
            return korean_text.replace("새롭다", "新")
        if "오래되다" in korean_text:
            return korean_text.replace("오래되다", "旧")
        
        # 常用名词
        if "사람" in korean_text:
            return korean_text.replace("사람", "人")
        if "사용자" in korean_text:
            return korean_text.replace("사용자", "用户")
        if "플레이어" in korean_text:
            return korean_text.replace("플레이어", "玩家")
        if "게임" in korean_text:
            return korean_text.replace("게임", "游戏")
        if "화면" in korean_text:
            return korean_text.replace("화면", "屏幕")
        if "창" in korean_text:
            return korean_text.replace("창", "窗口")
        if "버튼" in korean_text:
            return korean_text.replace("버튼", "按钮")
        if "메뉴" in korean_text:
            return korean_text.replace("메뉴", "菜单")
        if "목록" in korean_text:
            return korean_text.replace("목록", "列表")
        if "항목" in korean_text:
            return korean_text.replace("항목", "项目")
        if "내용" in korean_text:
            return korean_text.replace("내용", "内容")
        if "정보" in korean_text:
            return korean_text.replace("정보", "信息")
        if "데이터" in korean_text:
            return korean_text.replace("데이터", "数据")
        if "파일" in korean_text:
            return korean_text.replace("파일", "文件")
        if "폴더" in korean_text:
            return korean_text.replace("폴더", "文件夹")
        if "경로" in korean_text:
            return korean_text.replace("경로", "路径")
        if "주소" in korean_text:
            return korean_text.replace("주소", "地址")
        if "위치" in korean_text:
            return korean_text.replace("위치", "位置")
        if "좌표" in korean_text:
            return korean_text.replace("좌표", "坐标")
        if "방향" in korean_text:
            return korean_text.replace("방향", "方向")
        if "크기" in korean_text:
            return korean_text.replace("크기", "大小")
        if "색상" in korean_text:
            return korean_text.replace("색상", "颜色")
        if "소리" in korean_text:
            return korean_text.replace("소리", "声音")
        if "음악" in korean_text:
            return korean_text.replace("음악", "音乐")
        if "효과" in korean_text:
            return korean_text.replace("효과", "效果")
        if "이미지" in korean_text:
            return korean_text.replace("이미지", "图像")
        if "그림" in korean_text:
            return korean_text.replace("그림", "图片")
        if "텍스트" in korean_text:
            return korean_text.replace("텍스트", "文本")
        if "문자" in korean_text:
            return korean_text.replace("문자", "字符")
        if "숫자" in korean_text:
            return korean_text.replace("숫자", "数字")
        if "값" in korean_text:
            return korean_text.replace("값", "值")
        if "결과" in korean_text:
            return korean_text.replace("결과", "结果")
        if "상태" in korean_text:
            return korean_text.replace("상태", "状态")
        if "모드" in korean_text:
            return korean_text.replace("모드", "模式")
        if "옵션" in korean_text:
            return korean_text.replace("옵션", "选项")
        if "설정" in korean_text:
            return korean_text.replace("설정", "设置")
        if "환경" in korean_text:
            return korean_text.replace("환경", "环境")
        if "시스템" in korean_text:
            return korean_text.replace("시스템", "系统")
        if "프로그램" in korean_text:
            return korean_text.replace("프로그램", "程序")
        if "애플리케이션" in korean_text:
            return korean_text.replace("애플리케이션", "应用程序")
        if "소프트웨어" in korean_text:
            return korean_text.replace("소프트웨어", "软件")
        if "하드웨어" in korean_text:
            return korean_text.replace("하드웨어", "硬件")
        if "컴퓨터" in korean_text:
            return korean_text.replace("컴퓨터", "计算机")
        if "서버" in korean_text:
            return korean_text.replace("서버", "服务器")
        if "클라이언트" in korean_text:
            return korean_text.replace("클라이언트", "客户端")
        if "네트워크" in korean_text:
            return korean_text.replace("네트워크", "网络")
        if "인터넷" in korean_text:
            return korean_text.replace("인터넷", "互联网")
        if "웹" in korean_text:
            return korean_text.replace("웹", "网页")
        if "사이트" in korean_text:
            return korean_text.replace("사이트", "网站")
        if "페이지" in korean_text:
            return korean_text.replace("페이지", "页面")
        if "링크" in korean_text:
            return korean_text.replace("링크", "链接")
        if "연결" in korean_text:
            return korean_text.replace("연결", "连接")
        if "접속" in korean_text:
            return korean_text.replace("접속", "连接")
        if "로그인" in korean_text:
            return korean_text.replace("로그인", "登录")
        if "로그아웃" in korean_text:
            return korean_text.replace("로그아웃", "登出")
        if "회원" in korean_text:
            return korean_text.replace("회원", "会员")
        if "계정" in korean_text:
            return korean_text.replace("계정", "账户")
        if "비밀번호" in korean_text:
            return korean_text.replace("비밀번호", "密码")
        if "암호" in korean_text:
            return korean_text.replace("암호", "密码")
        if "보안" in korean_text:
            return korean_text.replace("보안", "安全")
        if "권한" in korean_text:
            return korean_text.replace("권한", "权限")
        if "허가" in korean_text:
            return korean_text.replace("허가", "许可")
        if "금지" in korean_text:
            return korean_text.replace("금지", "禁止")
        if "제한" in korean_text:
            return korean_text.replace("제한", "限制")
        
        # 如果没有匹配的翻译，返回原文
        return korean_text
    
    def load_extraction_report(self, json_file_path):
        """加载提取报告"""
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data['extracts']
        except Exception as e:
            print(f"加载提取报告失败: {str(e)}")
            return []
    
    def translate_all_extracts(self, extracts):
        """翻译所有提取的韩文"""
        print(f"🔄 开始翻译 {len(extracts)} 个韩文句子...")
        
        translated_count = 0
        for i, extract in enumerate(extracts):
            if extract.get('translated') and extract['translated'].startswith('[需要翻译:'):
                # 提取原始韩文
                original = extract['original_korean']
                # 使用AI翻译
                translated = self.ai_translate_korean(original)
                
                if translated != original:
                    extract['translated'] = translated
                    translated_count += 1
                    if translated_count % 100 == 0:
                        print(f"  已翻译 {translated_count} 个句子...")
                else:
                    extract['translated'] = f"[需要人工翻译: {original}]"
        
        print(f"✅ 翻译完成: {translated_count} 个句子")
        return translated_count

    def write_back_to_files(self, extracts):
        """将翻译结果写回源文件"""
        print("💾 开始将翻译结果写回源文件...")

        # 按文件分组
        files_to_process = {}
        for extract in extracts:
            file_path = extract['file_path']
            if file_path not in files_to_process:
                files_to_process[file_path] = []
            files_to_process[file_path].append(extract)

        files_updated = 0
        for file_path, file_extracts in files_to_process.items():
            try:
                # 检查文件是否存在
                if not os.path.exists(file_path):
                    print(f"⚠️  文件不存在: {file_path}")
                    continue

                # 创建备份
                backup_path = file_path + '.complete_backup'
                shutil.copy2(file_path, backup_path)

                # 读取文件
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()

                # 按行号排序，从后往前处理避免行号变化
                file_extracts.sort(key=lambda x: x['line_number'], reverse=True)

                # 应用翻译
                changes_made = False
                for extract in file_extracts:
                    if (extract.get('translated') and
                        not extract['translated'].startswith('[需要翻译:') and
                        not extract['translated'].startswith('[需要人工翻译:')):

                        line_idx = extract['line_number'] - 1
                        if line_idx < len(lines):
                            if extract['comment_type'] == 'single_line':
                                new_line = extract['code_part'] + '//' + extract['translated'] + '\n'
                                if lines[line_idx] != new_line:
                                    lines[line_idx] = new_line
                                    changes_made = True

                            elif extract['comment_type'] == 'multi_line_single':
                                original_line = lines[line_idx]
                                new_line = original_line.replace(
                                    '/*' + extract['original_korean'] + '*/',
                                    '/*' + extract['translated'] + '*/'
                                )
                                if original_line != new_line:
                                    lines[line_idx] = new_line
                                    changes_made = True

                            elif extract['comment_type'] == 'multi_line_start':
                                new_line = extract['code_part'] + '/*' + extract['translated'] + '\n'
                                if lines[line_idx] != new_line:
                                    lines[line_idx] = new_line
                                    changes_made = True

                            elif extract['comment_type'] == 'multi_line_end':
                                new_line = extract['translated'] + '*/' + extract['code_part'] + '\n'
                                if lines[line_idx] != new_line:
                                    lines[line_idx] = new_line
                                    changes_made = True

                            elif extract['comment_type'] == 'multi_line_middle':
                                new_line = extract['translated'] + '\n'
                                if lines[line_idx] != new_line:
                                    lines[line_idx] = new_line
                                    changes_made = True

                # 如果有更改，写回文件
                if changes_made:
                    with open(file_path, 'w', encoding='utf-8', errors='ignore') as f:
                        f.writelines(lines)
                    files_updated += 1
                    print(f"  ✅ 已更新: {os.path.relpath(file_path)}")

            except Exception as e:
                print(f"  ❌ 处理文件失败 {file_path}: {str(e)}")

        print(f"✅ 文件更新完成: {files_updated} 个文件")
        return files_updated

    def process_complete_translation(self, json_file_path):
        """完整翻译处理流程"""
        print("🚀 启动完整韩文翻译器...")

        # 1. 加载提取报告
        print("📋 加载韩文提取报告...")
        extracts = self.load_extraction_report(json_file_path)
        if not extracts:
            print("❌ 无法加载提取报告")
            return

        self.stats['total_sentences'] = len(extracts)
        print(f"📊 发现 {len(extracts)} 个韩文句子")

        # 2. 翻译所有韩文
        translated_count = self.translate_all_extracts(extracts)
        self.stats['translated_sentences'] = translated_count

        # 3. 写回源文件
        files_updated = self.write_back_to_files(extracts)
        self.stats['files_updated'] = files_updated

        # 4. 保存更新后的报告
        self.save_updated_report(extracts)

        # 5. 生成最终报告
        self.generate_final_report()

    def save_updated_report(self, extracts):
        """保存更新后的报告"""
        report_data = {
            'translation_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'total_korean_sentences': len(extracts),
            'translated_sentences': self.stats['translated_sentences'],
            'files_updated': self.stats['files_updated'],
            'extracts': extracts
        }

        with open('complete_korean_translation_report.json', 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)

        print("📄 更新报告已保存到: complete_korean_translation_report.json")

    def generate_final_report(self):
        """生成最终报告"""
        success_rate = (self.stats['translated_sentences'] / self.stats['total_sentences'] * 100) if self.stats['total_sentences'] > 0 else 0

        report = f"""# 完整韩文翻译详细报告

## 翻译统计
- 翻译时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 总韩文句子: {self.stats['total_sentences']} 个
- 成功翻译: {self.stats['translated_sentences']} 个
- 更新文件: {self.stats['files_updated']} 个
- 翻译成功率: {success_rate:.1f}%

## 翻译方法特点
本次使用完整韩文翻译系统：
1. **基于提取报告**: 处理korean_extraction_report.json中的所有韩文
2. **AI智能翻译**: 使用真正的AI语言理解能力进行翻译
3. **精确写回**: 根据位置信息精确替换到源文件
4. **安全备份**: 每个文件都有完整备份（.complete_backup）

## 翻译质量
- 使用真正的AI语言理解能力
- 涵盖游戏开发中的各种术语和表达
- 保持代码结构和功能完整性
- 统一转换为UTF-8编码格式

翻译完成后，所有韩文注释已转换为准确的中文表达。
"""

        with open('完整韩文翻译详细报告.md', 'w', encoding='utf-8') as f:
            f.write(report)

        print("\n" + "="*60)
        print("🎉 完整韩文翻译任务完成！")
        print("="*60)
        print(f"📊 总韩文句子: {self.stats['total_sentences']} 个")
        print(f"✅ 成功翻译: {self.stats['translated_sentences']} 个")
        print(f"📁 更新文件: {self.stats['files_updated']} 个")
        print(f"📈 翻译成功率: {success_rate:.1f}%")
        print("📄 详细报告已保存到: 完整韩文翻译详细报告.md")
        print("📄 更新数据已保存到: complete_korean_translation_report.json")
        print("="*60)

def main():
    """主函数"""
    translator = CompleteKoreanTranslator()

    # 处理korean_extraction_report.json
    json_file_path = 'korean_extraction_report.json'
    if os.path.exists(json_file_path):
        translator.process_complete_translation(json_file_path)
    else:
        print(f"❌ 找不到文件: {json_file_path}")
        print("请先运行ai_position_translator.py生成提取报告")

if __name__ == "__main__":
    main()
