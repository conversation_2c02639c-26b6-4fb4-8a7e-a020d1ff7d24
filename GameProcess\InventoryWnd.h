#ifndef _INVENTORYWND_H
#define _INVENTORYWND_H


#pragma pack(1)
typedef struct tagITEMSET
{
	BOOL	bSetted;
	CItem	xItem;

	//추가 가방
	BOOL	bIsInvenExWnd;
}ITEMSET, *LPITEMSET;
typedef struct tagCOMMONITEMSET
{
	BOOL	bSetted;					//아越템越 세팅되었는지的 여부。
	BOOL	bWaitResponse;				//当前 아越템越 먹或者，装备되어西 西버的 응답을 기다리는지的 여부。
	BOOL	bIsEquipItem;				//当前 아越템越 장착窗口用/向从 왔는지的 여부。
	BOOL	bIsDealItem;				//当前 아越템越 장착窗口用/向从 왔는지的 여부。
	BOOL	bIsBeltItem;				//当前 아越템越 벨트窗口用/向从 왔는지的 여부。
	BOOL	bIsHideItem;				//鼠标상태的 아越템을 보여줄것인가？。
	BOOL	bIsUtilItem;				//유틸 윈刀우从/在 사용한 아越템인가？
	CItem	xItem;						//아越템。

	//추가 가방
	BOOL	bIsInvenWnd;				//원래 가방
	BOOL	bIsInvenExWnd;				//추가 가방
}COMMONITEMSET, *LPCOMMONITEMSET;
typedef struct tagINVENITEMSET
{
	BOOL	bSetted;
	INT		nWidth;
	INT		nHeight;
	CItem	xItem;
}INVENITEMSET, *LPINVENITEMSET;

typedef struct tagPARKITEM
{
	BOOL	bSetted;
	BYTE	bRecvPrice;
	INT		nCellNum;
	INT		nItemNum;
}PARKITEM, *LPPARKITEM;
#pragma pack(8)



class CInventoryWnd : public CGameWnd
{ 
protected:
	BYTE		m_bType;							//가방窗口，수리窗口，商店팔기窗口。

	INT			m_nStartLineNum;					//背包窗口的 처음 라인的 번호。(총 10줄越고 화면在는 4줄 到 볼수 有/存在。)

	CGameBtn	m_xInventoryBtn[_MAX_INVEN_BTN];
	CScrlBar	m_xInvenScrlBar;

public:
	SHORT		 m_shItemSetInfo[_INVEN_TOTAL_CELL];
	INVENITEMSET m_stInventoryItem[_MAX_INVEN_ITEM];

	PARKITEM	 m_stParkItem;
	INT			 m_nMerchantID;
	CHAR		 m_pszPrice[20];
	INT			 m_nPrice;

	DWORD		 m_dwDLClickTime;
	DWORD		 m_dwLClickTime;
	DWORD		 m_dwRClickTime;


public:
	CInventoryWnd();
	~CInventoryWnd();

	virtual VOID Init();
	virtual VOID Destroy();

	VOID	CreateInventoryWnd(INT nID, CWHWilImageData* pxWndImage, INT nFrameImgIdx, INT nStartX, INT nStartY, INT nWidth, INT nHeight, BOOL bCanMove);
	VOID	ShowInventoryWnd();
	VOID	ShowInvenItemState();
	VOID	SetInvenBtnInit();

	INT		GetInvenItemNum(POINT ptMouse);
	INT		GetInvenCellNum(POINT ptMouse);
	INT		GetEmptyInvenNum();
	VOID	ShowInvenItem();
	BOOL	GetCellWH(WORD wLooks, INT& nCellWidth, INT& nCellHeight);
	BOOL	CanItemInsert(INT nCellNum, CItem* pxItem, RECT& rcCell);
	VOID	SetItemState(CItem* pxItem, INT nItemNum = -1, LPRECT lprcCell = NULL);

	VOID	InitParkItem();
	VOID	SetInvenType(BYTE bType);

	__inline BYTE GetInvenType()
	{
		return m_bType;
	}

	__inline VOID SetMerchantID(INT nID)
	{
		m_nMerchantID = nID;
	}

	LPINVENITEMSET FindInvenItem(WORD wLooks);
	
public:
	BOOL	DeleteInvenItem(INT nInvenItemNum);
	BOOL	DeleteInvenItem(INT nMakeIndex, CHAR* szName);
	VOID	DeleteAllInvenItem();

	VOID	AddInvenItem(CItem xItem, INT nCellNum, BOOL bUseInvenNum = TRUE);
	BOOL	ExtractInvenItem(CItem* pxItem, INT nInvenItemNum);
	VOID	ChangeInvenWithCommonOLD(INT nInvenCellNum, INT nInvenItemNum, LPCOMMONITEMSET pstCommonItemSet);
	VOID	ChangeInvenWithCommon(INT nInvenCellNum, INT nInvenItemNum, LPCOMMONITEMSET pstCommonItemSet);
	VOID	SetCommonFromInven(INT nInvenCellNum, INT nInvenItemNum, LPCOMMONITEMSET pstCommonItemSet);
	VOID	SetInvenFromCommonOLD(INT nInvenCellNum, LPCOMMONITEMSET pstCommonItemSet);
	VOID	SetInvenFromCommon(INT nInvenCellNum, LPCOMMONITEMSET pstCommonItemSet);

	VOID	SetCommonFromGold(LPCOMMONITEMSET pstCommonItemSet);

	BOOL	OnLButtonDoubleClick(LPCOMMONITEMSET pstCommonItemSet, POINT ptMouse);
	BOOL	OnLButtonDown(LPCOMMONITEMSET pstCommonItemSet, POINT ptMouse);
	BOOL	OnLButtonUp(LPCOMMONITEMSET pstCommonItemSet, POINT ptMouse);

	BOOL	OnRButtonDown(LPCOMMONITEMSET pstCommonItemSet, POINT ptMouse);

	BOOL	OnMouseMove(POINT ptMouse);
	VOID	OnScrollDown();
	VOID	OnScrollUp();

	VOID	ChangeItemCount(INT	nIdx, DWORD	wCount);
	BOOL	IsThereMultipleItem(CItem xItem);

	INT		GetSameItemCount(CItem xItem);
};



#endif // _INVENTORYWND_H