/******************************************************************************************************************
                                                                                                                   
	모듈명:																											
																													
	작성자:																											
	작성일:																											
																													
	[일자][수정자] : 수정 내용																						
                                                                                                                   
*******************************************************************************************************************/



#ifndef _CMAGIC_H
#define _CMAGIC_H



/******************************************************************************************************************

	CMagic Class
	

*******************************************************************************************************************/
class CMagic
{
/* Constructor & Destructor */
public:
	CMagic();
	virtual ~CMagic();

/* Attributes */
public:
	BYTE				m_bActiveState;							//마법 弓성화여부。(마법소멸小时在 사용된다。)
	BOOL				m_bRepeat;								//애니메越션的 반복여부。
	BOOL				m_bFixed;								//마법的 移动여부。
	WORD				m_wMagicNum;							//마법번호。

	CActor*				m_pxOwner;								//마법발사周체。
	CActor*				m_pxTarget;								//마법발사객체。

	D3DVERTEX			m_avMagic[4];
	CWHWilImageData*	m_pxMagicImg;

	//坐标정보。
	INT					m_nScrnX, m_nScrnY;								//마법스크린坐标。
	FLOAT				m_fFloatScrnX, m_fFloatScrnY;					//마법스크린坐标。
	INT					m_nTileX, m_nTileY;								//마법瓦片坐标。

	INT					m_nFireScrnX, m_nFireScrnY;						//마법생성스크린坐标。
	INT					m_nFireTileX, m_nFireTileY;						//마법생성瓦片坐标。

	INT					m_nTargetScrnX, m_nTargetScrnY;					//목표점的 스크린坐标。
	INT					m_nTargetTileX, m_nTargetTileY;					//목표점的 瓦片坐标。(변화될수有/存在。)

	//渲染정보。
	INT					m_nCurrDelay;									//지연时间保存값。
	INT					m_nCurrLightDelay;
	INT					m_nFrmDelay;									//帧을 넘기기 上한 지연时间값。
	DWORD				m_dwMagicLife;									//마법越 执行되고 있는 时间。(ms단上)

	BYTE				m_bLightRadius[2];								//광원 너비。
	BYTE				m_bLightColor[2][3];							//광원 색。
	BYTE				m_bMagicColor[3];								//색。

	DWORD				m_dwFstFrame;									//当前 마법的 小时작帧。
	DWORD				m_dwEndFrame;									//当前 마법的 마지막帧。
	DWORD				m_dwCurrFrame;									//帧保存값。

	BYTE				m_bBlendType;									//마법을 어떤형태用/向 그릴것인가를 결정。
	BYTE				m_bOpacity;										//0 - 255단계。0은 완전 투명(라越트형태)，255는 불투명。
	BYTE				m_bSwingCnt;									//마법的 흔들림 조건을 준다。

	FLOAT				m_fGradient;
	INT					m_nShiftX;
	INT					m_nShiftY;
	INT					m_nDistanceX;
	INT					m_nDistanceY;
	INT					m_nPrevDistanceX;
	INT					m_nPrevDistanceY;

	BYTE				m_bDir16;
	BYTE				m_bCurrSwing;

	BOOL				m_bShowLight;

	WORD				m_wStartDelay;	

	WORD				m_wFileType;
	WORD				m_wFileIdx;

	INT					m_nDuplicateNum;

	BOOL				m_bStartSound;
	BOOL				m_bMiddleSound;

	CHAR				m_szServerNotice[MAX_PATH];

	
/* Operation */
public:

	virtual BOOL CreateMagic(WORD wMagicNum, INT nFireTileX, INT nFireTileY, 
		                     INT nTargetTileX, INT nTargetTileY, CActor* pxOwner = NULL, INT nTarget = 0, WORD wStartDelay = 0, INT nAtom = 0, WORD wRed = 0, WORD wBlue = 0, WORD wGreen = 0);


	virtual VOID DestoryMagic();
	virtual BOOL UpdateMagic(INT nLoopTime);
	virtual BOOL DrawMagic();
	virtual BOOL DrawLight(CLightFog* xLightFog, INT nLoopTime);
};

class CConcenFireball : public CMagic
{
private:
	FLOAT   m_fRate;
	BOOL	m_bReverse;
public:
	virtual BOOL CreateMagic(WORD wMagicNum, INT nFireTileX, INT nFireTileY, 
		                     INT nTargetTileX, INT nTargetTileY, CActor* pxOwner = NULL, INT nTarget = 0, WORD wStartDelay = 0, BOOL bReverse = FALSE);
	virtual BOOL UpdateMagic(INT nLoopTime);
	virtual BOOL DrawMagic();
};


class CRefineAdd : public CMagic
{
private:
public:
	virtual BOOL CreateMagic(WORD wMagicNum, INT nFireTileX, INT nFireTileY, 
		                     INT nTargetTileX, INT nTargetTileY, CActor* pxOwner = NULL, INT nTarget = 0, WORD wStartDelay = 0);
	virtual BOOL UpdateMagic(INT nLoopTime);
	virtual BOOL DrawMagic();
};


// _SKILL_FIRE. _SKILL_ICE. _SKILL_SHOOTLIGHTENADD.
class CMagicStream : public CMagic
{
private:
	WORD	m_wMagicStart;
	POINT	m_ptTileGap;
public:
	BOOL CreateMagic(WORD wMagicNum, INT nFireTileX, INT nFireTileY, 
		             INT nTargetTileX, INT nTargetTileY, CActor* pxOwner, INT nTarget, WORD wStartMagicTime, POINT ptTileGap);
	BOOL UpdateMagic(INT nLoopTime);
	BOOL DrawMagic();
};


class CElecMagic : public CMagic
{
private:
	CElec m_xElec;

public:
	virtual BOOL CreateMagic(WORD wMagicNum, INT nFireTileX, INT nFireTileY, 
		                     INT nTargetTileX, INT nTargetTileY, CActor* pxOwner = NULL, INT nTarget = 0);
	virtual BOOL UpdateMagic(INT nLoopTime);
	virtual BOOL DrawMagic();
};



//_SKILL_HOLYSHIELD，_SKILL_EARTHFIRE，_SKILL_LIGHTWALL 전용。
class CRepeatMagic : public CMagic
{
private:
public:
	DWORD	m_dwMagiLifeTotal;
	INT		m_nEventID;					//越벤트用/向관리된다。

	_inline CRepeatMagic()
	{
		m_nEventID		  = 0;
		m_dwMagiLifeTotal = 0;
	}

	BOOL CreateMagic(INT nEventID, WORD wMagicNum, INT nFireTileX, INT nFireTileY, 
	                 INT nTargetTileX, INT nTargetTileY, DWORD dwMagicLifeTotal, CActor* pxOwner = NULL, INT nTarget = 0);
	BOOL UpdateMagic(INT nLoopTime);
	BOOL DrawMagic();
};

//_SKILL_HURRICANE 전용
class CHurricaneMagic : public CMagic
{
public:
	INT		mHurricaneMagic_nState;
	int		mHurricaneMagic_nOffsetX;
	int		mHurricaneMagic_nOffsetY;
	INT		mHurricaneMagic_nId;
	float	mHurricaneMagic_fRadius;
	INT		mHurricaneMagic_nIncrease;

	INT		mHurricaneMagic_nAddX;
	INT		mHurricaneMagic_nAddY;

	INT		mHurricaneMagic_nKind;

public:
	virtual	BOOL	CreateMagic(WORD wMagicNum, INT nFireTileX, INT nFireTileY, 
				                 INT nTargetTileX, INT nTargetTileY, INT nKind, CActor* pxOwner = NULL, INT nTarget = 0);
	BOOL	UpdateMagic(INT nLoopTime);
	BOOL	DrawMagic();
};

//ICE_WALL 전용
class CMiddleRepeateMagic : public CMagic
{
public:
	DWORD	m_dwMagiLifeTotal;
	INT		m_nEventID;					//越벤트用/向관리된다。


	BOOL CreateMagic(INT nEventID, WORD wMagicNum, INT nFireTileX, INT nFireTileY, 
	                 INT nTargetTileX, INT nTargetTileY, DWORD dwMagicLifeTotal, CActor* pxOwner = NULL, INT nTarget = 0);
	BOOL UpdateMagic(INT nLoopTime);
	BOOL DrawMagic();
};


#endif // _CMAGIC_H
