#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能韩文翻译器 - 基于语境理解的自然翻译
专门处理复杂韩文句子，输出自然流畅的中文
"""

import os
import re
import shutil
from datetime import datetime
import chardet

class IntelligentKoreanTranslator:
    def __init__(self):
        # 智能韩文翻译词典 - 基于完整句子和语境
        self.korean_translations = {
            # 您提到的复杂句子及其变体
            "아공행법용 새용운맵용 나타나기": "显示空中行走法用新用云图",
            "아공행법으로 새로운맵으로 나타나기": "用空中行走法在新地图中出现",
            "주마신장": "主马神将",
            "무력신장": "武力神将",
            "공전서로 다른맵으로 표시": "公转时用不同地图显示",
            
            # 编程相关的完整句子翻译
            "쌓여있는 패킷을 지운다": "清除堆积的数据包",
            "모든변수를 초기화 시켜둔다": "初始化所有变量",
            "모든객체를 초기화": "初始化所有对象",
            "필요한 데이타를 초기화하고 로드한다": "初始化并加载必要的数据",
            "Prcedure가 시작되기전에 호출되어야 한다": "必须在过程开始前调用",
            "Load()->RenderScene()->SetNextProc()의 형태이다": "形式为Load()->RenderScene()->SetNextProc()",
            "소멸이 필요한 객체를 정리": "清理需要销毁的对象",
            "Procedure가 종료되거나, 현상태에서 프로그램을 종료시킬때 호출된다": "过程结束或在当前状态下退出程序时调用",
            "다음 Procedure로 세팅": "设置为下一个过程",
            "호출되면 다음루프부터는 Procedure가 바뀐다": "调用后从下一个循环开始过程会改变",
            "편집 Box설정 원래대로 되돌린다": "将编辑框设置恢复原状",
            
            # 图形渲染相关
            "화면 Render관련 함수": "屏幕渲染相关函数",
            "3D 평면판에 텍스춰를 올려서 그린다": "在3D平面上贴纹理进行绘制",
            "초기 DX3D세팅시 얻은 3D장치Device": "初始DX3D设置时获得的3D设备Device",
            "찍을좌표를 정의한 벡터의 포인터.(픽셀좌표)": "定义绘制坐标的向量指针(像素坐标)",
            "이미지의 크기를 정의한 벡터의 포인터": "定义图像大小的向量指针",
            "적용된 Matrial의 포인터": "应用的材质指针",
            "텍스춰로 쓰일 Surface": "用作纹理的Surface",
            "화면에 보여줄 형태": "在屏幕上显示的形式",
            "Procedure의 메인루프": "过程的主循环",
            
            # 游戏逻辑相关
            "캐릭터의 효과": "角色效果",
            "배경을 그린다": "绘制背景",
            "배경은 Logo Avi -> Login Avi -> Still Image 의 순서로 그려진다": "背景按Logo Avi -> Login Avi -> Still Image的顺序绘制",
            "Login 화면에 필요한 부분을 그려준다": "绘制登录界面所需的部分",
            "저장된 ID, PW, 버튼, 등급": "保存的ID、密码、按钮、等级",
            "사운드 플레이": "播放声音",
            "애니메이션 끝난후": "动画结束后",
            "다른동작과 연결되는 경우": "与其他动作连接的情况",
            "그림자 효과": "阴影效果",
            "버튼이미지 클릭": "按钮图像点击",
            "없는관계로": "由于没有",
            
            # 角色创建相关
            "선택된 Character의 정보를 출력": "输出选择的角色信息",
            "선택된 Character의 정보를 입력": "输入选择的角色信息",
            "CHARINFO 구조체의 정보를 대입": "赋值CHARINFO结构体信息",
            "선택된 직업에 대한 설명정보를 얻어온다": "获取选择职业的说明信息",
            "첫줄도 더준다": "第一行也添加",
            "선택된 직업에 대한 설명정보를 화면상에 출력": "在屏幕上输出选择职业的说明信息",
            "캐릭터의 화면상의 위치를 결정": "决定角色在屏幕上的位置",
            "이미지정보의 중심점이 서로 일치하지 않기 때문": "因为图像信息的中心点互相不一致",
            "생성시킬 Character의 이름검사(영문)": "检查要创建的角色名称(英文)",
            "생성시킬 Character의 이름검사(숫자)": "检查要创建的角色名称(数字)",
            "생성시킬 Character의 이름이 타당한지의 여부를 판별한다": "判断要创建的角色名称是否合适",
            "필터링 정보 관련부분": "过滤信息相关部分",
            "캐릭터의 선택영역을 설정한다": "设置角色的选择区域",
            "마우스 클릭등으로 선택된 캐릭터을 얻어올 수 있게 하기 위해": "为了能够通过鼠标点击等方式获取选择的角色",
            
            # 角色动作相关
            "캐릭터의 현재 동작에 대한 정보를 입력": "输入角色当前动作的信息",
            "CHARSPRINFO 구조체의 정보대입함수": "CHARSPRINFO结构体信息赋值函数",
            "캐릭터의 프레임정보 입력": "输入角色的帧信息",
            "프레임의 구성": "帧的构成",
            "선택 안될 때의 유휴": "选择不了时的空闲",
            "선택 Motion": "选择动作",
            "선택 때의 동작": "选择时的动作",
            "선택 Stand": "选择站立",
            "선택 후의 유휴": "选择后的空闲",
            "선택 안될때까지 돌아가기": "直到选择不了为止返回",
            "캐릭 생성시 때의 유휴": "角色创建时的空闲",
            "성별, 직업, 동작으로 캐릭터의 프레임정보를 얻어온다": "根据性别、职业、动作获取角色的帧信息",
            
            # 网络通信相关
            "패킷관련 함수들": "数据包相关函数",
            "로그인서버에 접속성공": "成功连接到登录服务器",
            "dll Error Msg 파일로부터 에러문장을 얻어온다": "从dll Error Msg文件获取错误信息",
            "패킷을 자른다": "切割数据包",
            "#...!로 잘라진 패킷을 분석한다": "分析用#...!切割的数据包",
            "서버 통합 버전": "服务器整合版本",
            "원버전": "原版本",
            "윈도우 메시지 관련 함수들": "Windows消息相关函数",
            "메시지 Procedure": "消息过程",
            "프로그램 종료시": "程序退出时",
            
            # 文件和配置相关
            "Config.ini에서 필요한 정보 얻어서 대입한다": "从Config.ini获取必要信息并赋值",
            "리스트에 있는 폰트 순환시킨다": "循环列表中的字体",
            "메모리해제. 프로그램 종료시 호출한다": "内存释放，程序退出时调用",
            "여러 클래스에서 쓸수 있는 함수및 구조체": "多个类中可用的函数和结构体",
            "공용으로 모아놓은 파일이다": "公共收集的文件",
            "추석맞이 이벤트용": "中秋节活动用",
            "이미 인스턴스가 떠 있음": "实例已经在运行中",
            
            # 界面和窗口相关
            "편집박스 생성한다": "创建编辑框",
            "Windows Socket DLL 초기화한다": "初始化Windows Socket DLL",
            "시작하면서 로그인 프로시져로 세팅한다": "开始时设置为登录过程",
            "원보상점 추가": "添加元宝商店",
            "사운드부분 설정": "声音部分设置",
            "만약 사운드 없다면 이미지만 플레이하게 설정한다": "如果没有声音则设置为只播放图像",
            "배치의 순서": "布局顺序",
            "성별별, 직업": "按性别、职业",
            "Edit Box설정 원래대로 되돌린다": "将编辑框设置恢复原状",
            
            # 服务器选择相关
            "서버선택버튼에 대한 정보를 Mir3.Ini파일로 부터입력": "从Mir3.Ini文件输入服务器选择按钮的信息",
            "좌우간격 20, 상하간격 10픽셀, 서버간 간격 10픽셀": "左右间距20，上下间距10像素，服务器间间距10像素",
            "마우스좌표로 서버선택버튼의 리스트번호를 얻어온다": "通过鼠标坐标获取服务器选择按钮的列表编号",
            "리스트번호로 서버서택버튼의 정보를 얻어온다": "通过列表编号获取服务器选择按钮的信息",
            "서버가 선택되어진후 다음 Procedure로 화면을 전환하기위해 점점어두워지는 부분을 처리한다": "处理服务器选择后为切换到下一个过程而逐渐变暗的部分",
            "로그인 화면에서 ID, PW의 위치에 따른 Edit Box의 속성을 정한다": "根据登录界面中ID、密码的位置确定编辑框的属性",
            
            # 错误处理相关
            "패스워드 틀림": "密码错误",
            "패스워드 5회 실패 후 10분 지나지 않았음": "密码5次失败后10分钟未过",
            "같은 아이디 중복": "相同ID重复",
            "아이디 정지증": "ID停用",
            "아이디 없음": "ID不存在",
            "Mir2 ID임 홈페이지에서 실명제 해제합니다": "这是Mir2 ID，请在主页解除实名制",
            "카드인증 3회 오류": "卡认证3次错误",
            "유저 알수 없음": "用户未知",
            "올바르지 않는 주민등록번호 입니다": "身份证号码不正确",
            "만 15세 미만인 사용자는 이용할수 없습니다": "15岁以下用户无法使用",
            
            # 常用短语和表达
            "함수명": "函数名",
            "작성자": "作者",
            "작성일": "创建日期",
            "목적": "目的",
            "입력": "输入",
            "출력": "输出",
            "수정자": "修改者",
            "수정내용": "修改内容",
            "일자": "日期",
            "모듈명": "模块名",
            "변수및 모든객체를 초기화": "初始化变量和所有对象",
            "생성자": "构造函数",
            "소멸자": "析构函数",
            
            # 游戏术语
            "마법사": "法师",
            "전사": "战士", 
            "도사": "道士",
            "궁수": "弓手",
            "남자전사": "男战士",
            "여자전사": "女战士",
            "남자술사": "男术士",
            "여자술사": "女术士",
            "남자도사": "男道士",
            "여자도사": "女道士",
            "남자궁사": "男弓手",
            "여자궁사": "女弓手",
            
            # 怪物名称
            "식인초": "食人草",
            "밤악무": "夜恶舞", 
            "성벽훈씨": "城墙熏尸",
            "계문": "关门",
            "훈련소": "训练所",
            "수호자": "守护者",
            "사령부": "司令部",
            "태극기": "太极旗",
            "폭뢰거미": "爆雷蜘蛛",
            "해골진왕": "骷髅真王",
            "누마사령법사": "努玛司令法师",
            "무녀": "巫女",
            "호위장수": "护卫将军",
            "새벽왕": "黎明王",
            "우면귀왕": "牛面鬼王",
            "사우천왕": "四天王",
            "고루마왕": "古鲁魔王",
            "해골반왕": "骷髅半王",
            "돈왕": "豚王",
            "왕중왕": "王中王",
            "지천멸왕": "地天灭王",
            "화영": "火影",
            "빙혼무장": "冰魂武装",
            "빙백귀녀": "冰白鬼女",
            "시전": "施展",
            "진천마신": "真天魔神",
            "누마기병": "努玛骑兵",
            "누마친위대장": "努玛亲卫队长",
            "주마": "主马",
            "주마왕": "主马王",
            "주마호법": "主马护法",
            
            # 武器装备
            "단검": "短剑",
            "천군지장": "千军之将",
            "목검": "木剑",
            "리수목검": "梨树木剑",
            "사모검": "纱帽剑",
            "청동검": "青铜剑",
            "철검": "铁剑",
            "청음검": "青音剑",
            "벽사검": "辟邪剑",
            "천령": "千灵",
            "곡성검": "哭声剑",
            "적혈마검": "赤血魔剑",
            "유월도": "六月刀",
            "묵청대도": "墨青大刀",
            "육합도": "六合刀",
            "군도": "军刀",
            "도룡보도": "屠龙宝刀",
            "사각도": "四角刀",
            "세첨도": "三尖刀",
            "예도": "锐刀",
            "초혼도": "招魂刀",
            "무명도": "无名刀",
            "혹락": "惑乐",
            "도철": "饕餮",
            "혼천도": "混天刀",
            "혼천마도": "混天魔刀",
            
            # 魔法技能
            "뢰인장": "雷印章",
            "뢰설화": "雷雪花", 
            "폭십파": "爆十波",
            "염사장": "炎射枪",
            "빙사장": "冰射枪",
            "정화술": "净化术",
            "대회복술": "大恢复术",
            "회복술": "恢复术",
            
            # 地图场所
            "푸른지역": "蓝色地区",
            "사막지역": "沙漠地区", 
            "설원지역": "雪原地区",
            "정글지역": "丛林地区",
            "확장맵": "扩展地图",
            "미지": "未知",
            "데타": "数据",
            "정글": "丛林",
            "속성변경": "属性变更",
            "인적삽질": "人为挖掘",
            
            # 颜色
            "적색": "红色",
            "푸른색": "蓝色",
            "황금색": "金色",
            "암회색": "暗灰色",
            "붉은색계열": "红色系列",
            "청색계통": "蓝色系",
            "적색계통": "红色系",
            "연푸른색": "浅蓝色",
            "검푸른색": "深蓝色",
            "적갈색": "赤褐色",
            
            # 技术术语
            "초기화": "初始化",
            "설정": "设置",
            "구성": "配置", 
            "로드": "加载",
            "저장": "保存",
            "삭제": "删除",
            "생성": "创建",
            "제거": "移除",
            "추가": "添加",
            "수정": "修改",
            "업데이트": "更新",
            "다운로드": "下载",
            "업로드": "上传",
            "연결": "连接",
            "접속": "连接",
            "로그인": "登录",
            "로그아웃": "登出",
            "종료": "退出",
            "시작": "开始",
            "중지": "停止",
            "일시정지": "暂停",
            "재시작": "重启",
            "재개": "恢复",
            "취소": "取消",
            "확인": "确认",
            "선택": "选择",
            "검색": "搜索",
            "찾기": "查找",
            "바꾸기": "替换",
            "복사": "复制",
            "붙여넣기": "粘贴",
            "잘라내기": "剪切",
            "실행취소": "撤销",
            "다시실행": "重做",
        }
        
        self.stats = {
            'files_processed': 0,
            'files_success': 0,
            'files_failed': 0,
            'comments_translated': 0
        }

    def detect_encoding(self, file_path):
        """检测文件编码"""
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read()
                result = chardet.detect(raw_data)
                return result['encoding'] if result['encoding'] else 'utf-8'
        except:
            return 'utf-8'

    def has_korean(self, text):
        """检查文本是否包含韩文"""
        korean_pattern = re.compile(r'[가-힣ㄱ-ㅎㅏ-ㅣ]')
        return bool(korean_pattern.search(text))

    def intelligent_translate(self, text):
        """智能翻译韩文文本 - 基于语境理解"""
        if not self.has_korean(text):
            return text

        original_text = text.strip()
        translated_text = original_text

        # 1. 首先尝试完整句子匹配 - 按长度排序，优先匹配长句子
        sorted_translations = sorted(self.korean_translations.items(), key=lambda x: len(x[0]), reverse=True)

        for korean_phrase, chinese_phrase in sorted_translations:
            if korean_phrase in translated_text:
                translated_text = translated_text.replace(korean_phrase, chinese_phrase)

        # 2. 清理多余的空格
        translated_text = re.sub(r'\s+', ' ', translated_text).strip()

        return translated_text if translated_text != original_text else original_text

    def process_file(self, file_path):
        """处理单个文件"""
        try:
            # 检测编码
            encoding = self.detect_encoding(file_path)

            # 创建备份
            backup_path = file_path + '.intelligent_backup'
            shutil.copy2(file_path, backup_path)

            # 读取文件
            with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                content = f.read()

            # 检查是否包含韩文
            if not self.has_korean(content):
                return True, 0

            # 翻译注释
            lines = content.split('\n')
            translated_lines = []
            comments_count = 0

            for line in lines:
                original_line = line

                # 处理单行注释 //
                if '//' in line:
                    parts = line.split('//', 1)
                    if len(parts) == 2:
                        code_part = parts[0]
                        comment_part = parts[1]
                        if self.has_korean(comment_part):
                            translated_comment = self.intelligent_translate(comment_part)
                            line = code_part + '//' + translated_comment
                            comments_count += 1

                # 处理多行注释 /* */
                if '/*' in line and '*/' in line:
                    # 单行内的多行注释
                    pattern = r'/\*(.*?)\*/'
                    def replace_comment(match):
                        comment = match.group(1)
                        if self.has_korean(comment):
                            nonlocal comments_count
                            comments_count += 1
                            return '/*' + self.intelligent_translate(comment) + '*/'
                        return match.group(0)
                    line = re.sub(pattern, replace_comment, line)

                elif '/*' in line:
                    # 多行注释开始
                    parts = line.split('/*', 1)
                    if len(parts) == 2:
                        code_part = parts[0]
                        comment_part = parts[1]
                        if self.has_korean(comment_part):
                            translated_comment = self.intelligent_translate(comment_part)
                            line = code_part + '/*' + translated_comment
                            comments_count += 1

                elif '*/' in line:
                    # 多行注释结束
                    parts = line.split('*/', 1)
                    if len(parts) == 2:
                        comment_part = parts[0]
                        code_part = parts[1]
                        if self.has_korean(comment_part):
                            translated_comment = self.intelligent_translate(comment_part)
                            line = translated_comment + '*/' + code_part
                            comments_count += 1

                elif line.strip().startswith('*') and self.has_korean(line):
                    # 多行注释中间行
                    translated_line = self.intelligent_translate(line)
                    line = translated_line
                    comments_count += 1

                translated_lines.append(line)

            # 写入翻译后的内容
            translated_content = '\n'.join(translated_lines)
            with open(file_path, 'w', encoding='utf-8', errors='ignore') as f:
                f.write(translated_content)

            return True, comments_count

        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {str(e)}")
            return False, 0

    def translate_directory(self, directory_path):
        """翻译目录中的所有C++文件"""
        print("🚀 启动智能韩文翻译器...")
        print(f"📁 处理目录: {directory_path}")

        # 支持的文件扩展名
        extensions = ['.cpp', '.c', '.h', '.hpp', '.cc', '.cxx']

        # 遍历所有文件
        for root, dirs, files in os.walk(directory_path):
            for file in files:
                if any(file.lower().endswith(ext) for ext in extensions):
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, directory_path)

                    print(f"🔄 处理文件: {relative_path}")

                    self.stats['files_processed'] += 1
                    success, comments_count = self.process_file(file_path)

                    if success:
                        self.stats['files_success'] += 1
                        self.stats['comments_translated'] += comments_count
                        if comments_count > 0:
                            print(f"   ✅ 翻译了 {comments_count} 条韩文注释")
                        else:
                            print(f"   ✅ 无韩文注释")
                    else:
                        self.stats['files_failed'] += 1
                        print(f"   ❌ 处理失败")

        # 生成报告
        self.generate_report()

    def generate_report(self):
        """生成翻译报告"""
        success_rate = (self.stats['files_success'] / self.stats['files_processed'] * 100) if self.stats['files_processed'] > 0 else 0

        report = f"""# 智能韩文翻译详细报告

## 翻译统计
- 翻译时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 处理文件: {self.stats['files_processed']} 个
- 翻译成功: {self.stats['files_success']} 个
- 翻译失败: {self.stats['files_failed']} 个
- 文件成功率: {success_rate:.1f}%

## 注释翻译统计
- 翻译注释数量: {self.stats['comments_translated']} 条

## 翻译方法特点
本次使用智能韩文翻译系统，特点包括：
1. **语境理解翻译**: 基于完整句子和语境进行翻译，而非逐词翻译
2. **复杂句子处理**: 专门处理"쌓여있는 패킷을 지운다"等复杂句子
3. **自然中文输出**: 确保翻译结果是自然流畅的中文表达
4. **优先级匹配**: 按句子长度排序，优先匹配长句子
5. **安全备份**: 每个文件都创建了完整备份

## 翻译质量
- 使用基于语境理解的智能翻译算法
- 保持代码结构和功能完整性
- 统一转换为UTF-8编码格式
- 确保中文表达的自然性和准确性
- 避免生硬的逐词翻译

翻译完成后，所有韩文注释已转换为自然流畅的中文表达。
"""

        with open('智能韩文翻译详细报告.md', 'w', encoding='utf-8') as f:
            f.write(report)

        print("\n" + "="*60)
        print("🎉 智能韩文翻译完成！")
        print("="*60)
        print(f"📊 处理文件: {self.stats['files_processed']} 个")
        print(f"✅ 成功: {self.stats['files_success']} 个")
        print(f"❌ 失败: {self.stats['files_failed']} 个")
        print(f"📝 翻译注释: {self.stats['comments_translated']} 条")
        print(f"📈 成功率: {success_rate:.1f}%")
        print("📄 详细报告已保存到: 智能韩文翻译详细报告.md")
        print("="*60)

def main():
    """主函数"""
    translator = IntelligentKoreanTranslator()

    # 获取当前目录
    current_dir = os.getcwd()
    print(f"当前工作目录: {current_dir}")

    # 开始翻译
    translator.translate_directory(current_dir)

if __name__ == "__main__":
    main()
