﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <SccProjectName />
    <SccLocalPath />
    <ProjectGuid>{8A585ED4-A06C-FC6A-4588-4344CC8B544A}</ProjectGuid>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseOfMfc>false</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>.\_Bin\</OutDir>
    <IntDir>.\_Obj_Debug\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>.\_Bin\</OutDir>
    <IntDir>.\_Obj_Release\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <Optimization>Disabled</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <MinimalRebuild>true</MinimalRebuild>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <PreprocessorDefinitions>WIN32;_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\_Obj_Debug\</AssemblerListingLocation>
      <PrecompiledHeaderOutputFile>.\_Obj_Debug\_Oranze Library.pch</PrecompiledHeaderOutputFile>
      <ObjectFileName>.\_Obj_Debug\</ObjectFileName>
      <ProgramDataBaseFileName>.\_Obj_Debug\</ProgramDataBaseFileName>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0412</Culture>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\_Bin\_Oranze Library.bsc</OutputFile>
    </Bscmake>
    <Lib>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>_Bin\_Oranze Library_Debug.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <Optimization>MaxSpeed</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>WIN32;NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\_Obj_Release\</AssemblerListingLocation>
      <PrecompiledHeaderOutputFile>.\_Obj_Release\_Oranze Library.pch</PrecompiledHeaderOutputFile>
      <ObjectFileName>.\_Obj_Release\</ObjectFileName>
      <ProgramDataBaseFileName>.\_Obj_Release\</ProgramDataBaseFileName>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <Culture>0x0412</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\_Bin\_Oranze Library.bsc</OutputFile>
    </Bscmake>
    <Lib>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\_Bin\_Oranze Library.lib</OutputFile>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="base64.cpp" />
    <ClCompile Include="database.cpp" />
    <ClCompile Include="datelog.cpp" />
    <ClCompile Include="error.cpp" />
    <ClCompile Include="file.cpp" />
    <ClCompile Include="http.cpp" />
    <ClCompile Include="imsgfmt.cpp" />
    <ClCompile Include="mail.cpp" />
    <ClCompile Include="mime.cpp" />
    <ClCompile Include="netbase.cpp" />
    <ClCompile Include="netiocp.cpp" />
    <ClCompile Include="nettcp.cpp" />
    <ClCompile Include="netudp.cpp" />
    <ClCompile Include="pop3.cpp" />
    <ClCompile Include="prime.cpp" />
    <ClCompile Include="quotedpr.cpp" />
    <ClCompile Include="registry.cpp" />
    <ClCompile Include="script.cpp" />
    <ClCompile Include="stringex.cpp" />
    <ClCompile Include="syncobj.cpp" />
    <ClCompile Include="url.cpp" />
    <ClCompile Include="util.cpp" />
    <ClCompile Include="uucp.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="astar.h" />
    <ClInclude Include="base64.h" />
    <ClInclude Include="bstree.h" />
    <ClInclude Include="database.h" />
    <ClInclude Include="datatype.h" />
    <ClInclude Include="datelog.h" />
    <ClInclude Include="error.h" />
    <ClInclude Include="file.h" />
    <ClInclude Include="fsa.h" />
    <ClInclude Include="http.h" />
    <ClInclude Include="imsgfmt.h" />
    <ClInclude Include="indexmap.h" />
    <ClInclude Include="list.h" />
    <ClInclude Include="mail.h" />
    <ClInclude Include="map.h" />
    <ClInclude Include="mime.h" />
    <ClInclude Include="netbase.h" />
    <ClInclude Include="netiocp.h" />
    <ClInclude Include="nettcp.h" />
    <ClInclude Include="netudp.h" />
    <ClInclude Include="pop3.h" />
    <ClInclude Include="pqueue.h" />
    <ClInclude Include="prime.h" />
    <ClInclude Include="queue.h" />
    <ClInclude Include="quotedpr.h" />
    <ClInclude Include="registry.h" />
    <ClInclude Include="script.h" />
    <ClInclude Include="slist.h" />
    <ClInclude Include="stack.h" />
    <ClInclude Include="streambf.h" />
    <ClInclude Include="stringex.h" />
    <ClInclude Include="syncobj.h" />
    <ClInclude Include="url.h" />
    <ClInclude Include="util.h" />
    <ClInclude Include="uucp.h" />
    <ClInclude Include="vector.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>