
#include "StdAfx.h"

CWHQueue::CWHQueue()
{
}

CWHQueue::~CWHQueue()
{
	ClearQueue();
}

void CWHQueue::PushQ(BYTE* lpbtQ)
{
	m_MsgQueueMutex.lock();

	m_MsgQueue.push_back(lpbtQ);

	m_MsgQueueMutex.unlock();
}

BYTE* CWHQueue::PopQ()
{
	if (m_MsgQueue.empty())
	{
		return nullptr;
	}

	m_MsgQueueMutex.lock();

	BYTE* pbt = m_MsgQueue.front();
	m_MsgQueue.pop_front();

	m_MsgQueueMutex.unlock();

	return pbt;
}

void CWHQueue::ClearQueue()
{
	if (!m_MsgQueue.empty())
	{
		m_MsgQueueMutex.lock();

		for (auto iter = m_MsgQueue.cbegin(); iter != m_MsgQueue.cend(); )
		{
			BYTE* pszPacket = *iter;
			iter = m_MsgQueue.erase(iter);

			SAFE_DELETE_ARRAY(pszPacket);
		}

		m_MsgQueueMutex.unlock();
	}
}
