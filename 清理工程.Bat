for /f "delims=" %%a in ('dir /s /b /ad .vs') do (
	Rd /S /Q "%%a"
)

for /f "delims=" %%a in ('dir /s /b /ad *.tlog') do (
	Rd /S /Q "%%a"
)

Rd /S /Q .vs


Del *.cmake /F /Q /S

Del *.aps /F /Q /S

Del *.bsc /F /Q /S

Del *.cls /F /Q /S

Del *.db /F /Q /S

Del *.dsp /F /Q /S

Del *.dsw /F /Q /S

Del *.idb /F /Q /S

Del *.ilk /F /Q /S

Del *.lastbuildstate /F /Q /S

Del *.obj /F /Q /S

Del *.pch /F /Q /S

Del *.pdb /F /Q /S

Del *.plg /F /Q /S

Del *.positions /F /Q /S

Del *.sbr /F /Q /S

Del *.sdf /F /Q /S

Del *.suo /F /Q /S

Del *.tlog /F /Q /S

Del *.user /F /Q /S

Pause