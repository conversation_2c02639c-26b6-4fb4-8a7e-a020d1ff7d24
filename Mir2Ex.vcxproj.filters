﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{deeae0e8-b786-4c83-800c-f601849399c3}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{3bea84fc-a415-4f0d-a8c5-3b44de6c5274}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{6b238590-4d31-4e11-b253-2de989107b5e}</UniqueIdentifier>
      <Extensions>ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe</Extensions>
    </Filter>
    <Filter Include="Login Process">
      <UniqueIdentifier>{5e34b0dd-da90-4efa-a7c3-5005ef7f3fbf}</UniqueIdentifier>
    </Filter>
    <Filter Include="Game Precess">
      <UniqueIdentifier>{9e6db4a0-aa03-4022-969a-e185fcd7cf0b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Game Precess\Header">
      <UniqueIdentifier>{225d93bc-1586-4f39-a540-eda7f4e53f3f}</UniqueIdentifier>
    </Filter>
    <Filter Include="Game Precess\Source">
      <UniqueIdentifier>{4fe061eb-cd6c-4e93-b58d-c63a08827fe0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Common">
      <UniqueIdentifier>{d25b6775-2af5-42ea-851e-ab98493318cf}</UniqueIdentifier>
    </Filter>
    <Filter Include="Sound">
      <UniqueIdentifier>{f8040a70-bbbe-436f-b318-e55c674548a7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Avi">
      <UniqueIdentifier>{c6d8002c-8193-4cb8-96ca-b2b6ec870ed0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Chr Select Process">
      <UniqueIdentifier>{5a82e548-708f-487d-bbc1-993a8c06926c}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="CmdLineParser.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GFun.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Mir2Ex.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="StdAfx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="LoginProcess\LoginProc.cpp">
      <Filter>Login Process</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\Actor.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\BeltWnd.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\ChatPopWnd.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\ChatWnd.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\ClientSysMsg.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\ExchangeWnd.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\FaceImgUp.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\GameBtn.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\GameMsgBox.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\GameOverWnd.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\GameProc.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\GameWnd.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\GroupWnd.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\GuildWnd.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\HorseWnd.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\Interface.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\InventoryWnd.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\Item.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\LightFog.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\Magic.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\MagicShortcutWnd.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\MagicWnd.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\MapHandler.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\Market.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\MarketUp.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\Messenger.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\MiniMap.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\MiniMapInSiege.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\NoticeEditWnd.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\NPCWnd.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\OptionWnd.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\Particle.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\PathFinding.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\QuestWnd.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\SettingWnd.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\SiegeWnd.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\SprDfn.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\StatusWnd.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\StoreWnd.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\UserStateWnd.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\UtilWnd.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\ViewMiniMapWnd.cpp">
      <Filter>Game Precess\Source</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\InventoryExWnd.cpp">
      <Filter>Game Precess</Filter>
    </ClCompile>
    <ClCompile Include="GameProcess\MarketEx.cpp">
      <Filter>Game Precess</Filter>
    </ClCompile>
    <ClCompile Include="Common\ChatEditBox.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\ClientSocket.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\crypto.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\DblList.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\DLinkedList.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\EnDecode.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\ImageHandler.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\ImageHandlerHelper.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\Msg.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\NoticeBox.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\Queue.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\ScreenCapture.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Common\StringSplitter.cpp">
      <Filter>Common</Filter>
    </ClCompile>
    <ClCompile Include="Sound\BMMP3.cpp">
      <Filter>Sound</Filter>
    </ClCompile>
    <ClCompile Include="Sound\DSound.cpp">
      <Filter>Sound</Filter>
    </ClCompile>
    <ClCompile Include="Sound\SoundManager.cpp">
      <Filter>Sound</Filter>
    </ClCompile>
    <ClCompile Include="Sound\WaveBuffer.cpp">
      <Filter>Sound</Filter>
    </ClCompile>
    <ClCompile Include="AVI\Avi.cpp">
      <Filter>Avi</Filter>
    </ClCompile>
    <ClCompile Include="CharSelectProcess\ChrProc.cpp">
      <Filter>Chr Select Process</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="Resource.rc">
      <Filter>Source Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="CmdLineParser.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Define.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Extern.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GFun.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="StdAfx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Common\Typedeftxt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="LoginProcess\LoginProc.h">
      <Filter>Login Process</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\Actor.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\BeltWnd.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\ChatPopWnd.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\ChatWnd.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\ClientSysMsg.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\ExchangeWnd.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\FaceImgUp.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\GameBtn.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\GameMsgBox.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\GameOverWnd.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\GameProc.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\GameWnd.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\GroupWnd.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\GuildWnd.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\HorseWnd.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\Interface.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\InventoryWnd.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\Item.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\LightFog.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\Magic.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\MagicShortcutWnd.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\MagicWnd.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\MapHandler.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\Market.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\MarketUp.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\Messenger.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\MiniMap.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\MiniMapInSiege.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\NoticeEditWnd.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\NPCWnd.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\OptionWnd.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\Particle.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\PathFinding.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\QuestWnd.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\SettingWnd.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\SiegeWnd.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\SprDfn.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\StatusWnd.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\StoreWnd.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\UserStateWnd.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\UtilWnd.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\ViewMiniMapWnd.h">
      <Filter>Game Precess\Header</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\InventoryExWnd.h">
      <Filter>Game Precess</Filter>
    </ClInclude>
    <ClInclude Include="GameProcess\MarketEx.h">
      <Filter>Game Precess</Filter>
    </ClInclude>
    <ClInclude Include="Common\ChatEditBox.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\ClientSocket.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\crypto.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\DblList.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\DLinkedList.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\EnDecode.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\ImageHandler.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\ImageHandlerHelper.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\list.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\Msg.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\NoticeBox.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\Protocol.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\Queue.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\ScreenCapture.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Common\StringSplitter.h">
      <Filter>Common</Filter>
    </ClInclude>
    <ClInclude Include="Sound\BMMP3.h">
      <Filter>Sound</Filter>
    </ClInclude>
    <ClInclude Include="Sound\DSound.h">
      <Filter>Sound</Filter>
    </ClInclude>
    <ClInclude Include="Sound\SoundManager.h">
      <Filter>Sound</Filter>
    </ClInclude>
    <ClInclude Include="Sound\TypeDef.h">
      <Filter>Sound</Filter>
    </ClInclude>
    <ClInclude Include="Sound\WaveBuffer.h">
      <Filter>Sound</Filter>
    </ClInclude>
    <ClInclude Include="AVI\Avi.h">
      <Filter>Avi</Filter>
    </ClInclude>
    <ClInclude Include="CharSelectProcess\ChrProc.h">
      <Filter>Chr Select Process</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <Text Include="ReadMe.txt" />
  </ItemGroup>
  <ItemGroup>
    <Library Include="_Oranze Library\_Bin\_Oranze Library.lib" />
  </ItemGroup>
</Project>