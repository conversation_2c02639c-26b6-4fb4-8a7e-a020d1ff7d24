/******************************************************************************************************************
                                                                                                                   
	모듈명:																											
																													
	작성자:																											
	작성일:																											
																													
	[일자][수정자] : 수정 내용																						
                                                                                                                   
*******************************************************************************************************************/


#ifndef _MAPHANDLER_
#define	_MAPHANDLER_


#pragma pack(1)
typedef struct tagRGBINFO
{
	BYTE	bRed;
	BYTE	bGreen;
	BYTE	bBlue;
}RGBINFO, *LPRGBINFO;

typedef struct tagLIGHTINFO
{
	BOOL	bIsLight;
	CHAR	cLightSizeType;
	CHAR	cLightColorType;
}LIGHTINFO, *LPLIGHTINFO;

//地图파日 加载관련 정보保存。
//当前 있는 지역的 전체 地图在 관련된 사항을 보관한다。
typedef struct tagMAPFILEHEADER
{
	CHAR	szDesc[20];
	WORD	wAttr;
	SHORT	shWidth;
	SHORT	shHeight;
	CHAR	cEventFileIdx;
	CHAR	cFogColor;
}MAPFILEHEADER, *LPMAPFILEHEADER;

typedef struct tagTILEINFO
{
	BYTE	bFileIdx;
	WORD	wTileIdx;
}TILEINFO, *LPTILEINFO;

typedef struct tagCELLINFO
{
	BYTE	bFlag;
	BYTE	bObj1Ani;
	BYTE	bObj2Ani;
	WORD	wFileIdx;
	WORD	wObj1;
	WORD	wObj2;
	BYTE	bDoorIdx;
	WORD	bDoorOffset;
	WORD	wLigntNEvent;
}CELLINFO, *LPCELLINFO;
/*
typedef struct tagDOORIMAGEINFO
{
	WORD	wPosX;
	WORD	wPosY;
	WORD	wImageNum;
}DOORIMAGEINFO, *LPDOORIMAGEINFO;

typedef struct tagDOORINFO
{
	BYTE			bDoorIdx;
	BYTE			bDoorImgCnt;
	LPDOORIMAGEINFO	pstDoorImgInfo;
}DOORINFO, *LPDOORINFO;
*/
#pragma pack(8)


class CMapHandler
{
private:
protected:
public:
	BOOL			m_bMapLoaded;

	CHAR			m_pszMapName[MAX_PATH];

	LPTILEINFO		m_pstTileInfo;
	LPCELLINFO		m_pstCellInfo;

//	CHAR*			 m_pcPath;
//	CAStar<CMapNode> m_xPathFinder;


	MAPFILEHEADER	m_stMapFileHeader;				//加载된 地图파日的 헤더。

	//角色的 移动和 관련된다。
	INT				m_nStartViewTileX;				//뷰区域的 小时작 X Tile 坐标。(실제 스크린 小时작 瓦片 坐标比 一点 더 큰 之间즈越다。)
	INT				m_nStartViewTileY;				//뷰区域은 실제 스크린 区域比 가用/向，세用/向 1瓦片씩을 여分钟用/向 둔다。
	INT				m_nViewOffsetX;					//스크롤한 X축한 길越。(角色的 走路东작的 帧。)
	INT				m_nViewOffsetY;				

	INT				m_nViewPosX;
	INT				m_nViewPosY;

	RECT			m_rcView;						//地图기본瓦片越 그려挖掘 区域。(화면클리핑을 上해西)
	RECT			m_rcMemView;

	POINT			m_ptMovePixelArray[6];			//移动을 上한 픽셀값的 보관데越타。

	BOOL			m_bUseTileBuffer;				//保存해둔 瓦片버퍼已经지를 越용한것인가的 여부。

	BYTE			m_bLightSize[4];
	RGBINFO			m_stRGBInfo[10];

	WORD			m_wSavedMapBuffer[_VIEW_CELL_X_COUNT*_CELL_WIDTH*_VIEW_CELL_Y_COUNT*_CELL_HEIGHT];				//기본瓦片和 SM瓦片을 记录한 버퍼。자기 角色가 移动하기전엔 갱신되지 않는다。


	DWORD			m_dwAniSaveTime[8];
	BYTE			m_bAniTileFrame[8][16];

	CWHWilImageData*	m_pxTileImg[_MAX_TILE_FILE];
/*
	BYTE			m_bDoorCount;
	LPDOORINFO		m_pstDoorInfo;
	BYTE*			m_pbCellIDoorIdx;
*/

public:
	CMapHandler();
	~CMapHandler();
	VOID			InitMapHandler();				//클래스 정보 秒기화。
	VOID			DestroyMapHandler();			//클래스 정보 秒기화및 메모리 해제。

	VOID			LoadMemViewBuffer(INT nX, INT nY/*, INT nLoopTime*/);

	BOOL			LoadMapData(CHAR* szMapFile);	//적용되는 地图的 데越타를 加载。(地图越 바뀔时候每 불려진다。)
	VOID			LoadNewMapBuffer();				//地图越 처음 加载되었을时候 瓦片，SM瓦片 记录버퍼在 최秒的 내용을 적용한다。

	VOID			FreeMapData();					//적용되는 地图的 데越타를 해제。(地图越 바뀔时候每 불려진다。)

	VOID			DrawBaseTile(INT nX, INT nY);	//기본瓦片을 그린다。
	VOID			DrawObjTile(INT nX, INT nY, BYTE bObjNum);
	VOID			DrawObjOneCellTile(INT nX, INT nY, BYTE bObjNum);

//	VOID			DrawOpenDoor(INT nX, INT nY);
//	VOID			SetDoorIndex();

	VOID			ScrollMap(INT nCharDir, INT nCharFrame, INT nSpeed);		//角色 移动小时 地图的 Scroll관련 변수들 적용。
	VOID			ScrollMap(INT nCharDir, WORD wCurrDelay, WORD wMoveDelay, INT nSpeed);
	VOID			SetMovedTileBuffer(INT nMovedTileX, INT nMovedTileY);		//角色 移动小时 새用/向 갱신되는 기본 已经지(瓦片，SM瓦片)를 적용。
	VOID			DrawTile();

	VOID			SetStartViewTile(INT nX, INT nY);							//그리는 뷰区域的 秒기치세팅。

	BOOL			GetNextTileCanMove(INT nXPos, INT nYPos, BYTE bDir, INT nGap, POINT* lpptTarget);
	VOID			GetLastTile(INT nXPos, INT nYPos, BYTE bDir, INT& nTileX, INT& nTileY, INT nGap = 1);

/////////////////////////////////////////////////////////////////////////////////////////////////////
	VOID			GetScrnPosFromTilePos(INT nScrnX, INT nScrnY, INT& nTileX, INT& nTileY);
	VOID			GetTilePosFromScrnPos(INT nScrnX, INT nScrnY, INT& nTileX, INT& nTileY);

	BYTE			CalcDirection16(INT nFireTileX, INT nFireTileY, INT nDestTileX, INT nDestTileY);
	BYTE			CalcDirection8(INT nFireTileX, INT nFireTileY, INT nDestTileX, INT nDestTileY);
	BYTE			GetNextDirection(INT nSX, INT nSY, INT nTX, INT nTY);

	BOOL			PathFinding(INT nSX, INT nSY, INT nEX, INT nEY);

//当前 瓦片在 대한 地图属性 얻기 함수들。
	BYTE			GetTileAttribute(INT nX, INT nY);
	BYTE			GetEventNum(INT nX, INT nY);
//	BOOL			GetDoorState(INT nX, INT nY);

	INT				GetDoor(INT nX, INT nY);
	VOID			OpenDoor(INT nX, INT nY, INT nIdx);
	VOID			CloseDoor(INT nX, INT nY, INT nIdx);
	INT				GetDoorImgIdx(INT nX, INT nY);
	BOOL			IsDoorOpen(INT nX, INT nY);

	LIGHTINFO		GetTileLightInfo(INT nX, INT nY);
//	VOID			SetDoorState(BYTE bIndex, BOOL bDoorOpen);
	VOID			SetAniTileFrame(INT nLoopTime);
};


#endif //_MAPHANDLER_
