D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\AVI\Avi.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\Avi.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\CharSelectProcess\ChrProc.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\ChrProc.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\CmdLineParser.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\CmdLineParser.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Common\ChatEditBox.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\ChatEditBox.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Common\ClientSocket.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\ClientSocket.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Common\crypto.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\crypto.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Common\DblList.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\DblList.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Common\DLinkedList.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\DLinkedList.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Common\EnDecode.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\EnDecode.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Common\ImageHandler.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\ImageHandler.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Common\ImageHandlerHelper.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\ImageHandlerHelper.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Common\Msg.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\Msg.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Common\NoticeBox.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\NoticeBox.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Common\Queue.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\Queue.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Common\ScreenCapture.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\ScreenCapture.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Common\StringSplitter.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\StringSplitter.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\Actor.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\Actor.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\BeltWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\BeltWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\ChatPopWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\ChatPopWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\ChatWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\ChatWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\ClientSysMsg.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\ClientSysMsg.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\ExchangeWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\ExchangeWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\FaceImgUp.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\FaceImgUp.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\GameBtn.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\GameBtn.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\GameMsgBox.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\GameMsgBox.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\GameOverWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\GameOverWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\GameProc.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\GameProc.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\GameWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\GameWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\GroupWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\GroupWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\GuildWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\GuildWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\HorseWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\HorseWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\Interface.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\Interface.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\InventoryExWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\InventoryExWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\InventoryWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\InventoryWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\Item.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\Item.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\LightFog.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\LightFog.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\Magic.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\Magic.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\MagicShortcutWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\MagicShortcutWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\MagicWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\MagicWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\MapHandler.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\MapHandler.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\Market.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\Market.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\MarketEx.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\MarketEx.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\MarketUp.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\MarketUp.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\Messenger.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\Messenger.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\MiniMap.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\MiniMap.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\MiniMapInSiege.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\MiniMapInSiege.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\NoticeEditWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\NoticeEditWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\NPCWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\NPCWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\OptionWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\OptionWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\Particle.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\Particle.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\PathFinding.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\PathFinding.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\QuestWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\QuestWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\SettingWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\SettingWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\SiegeWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\SiegeWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\SprDfn.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\SprDfn.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\StatusWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\StatusWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\StoreWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\StoreWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\UserStateWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\UserStateWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\UtilWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\UtilWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GameProcess\ViewMiniMapWnd.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\ViewMiniMapWnd.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\GFun.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\GFun.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\LoginProcess\LoginProc.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\LoginProc.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\Mir2Ex.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Sound\BMMP3.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\BMMP3.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Sound\DSound.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\DSound.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Sound\SoundManager.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\SoundManager.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Sound\WaveBuffer.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\WaveBuffer.obj
D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\StdAfx.cpp;D:\Lothario\Documents\Visual Studio 2022\Projects\Mir3_Client\Mir2Ex___Win32_Test_Debug\StdAfx.obj
