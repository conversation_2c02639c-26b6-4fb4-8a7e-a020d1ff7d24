// ****************************************************************************
//
//		Registry Handler 
//
//      All written by <PERSON><PERSON>, <PERSON><PERSON>-heon
//      Copyright(C) 1997, 1998 <PERSON><PERSON>-heon, <PERSON><PERSON>. All right reserved.
//
// ****************************************************************************

#include "stdafx.h"



BOOL jRegSetKey(LPCTSTR pSubKeyName, LPCTSTR pValueName, DWORD dwFlags, 
				LPBYTE pValue, DWORD nValueSize);
BOOL jRegGetKey(LPCTSTR pSubKeyName, LPCTSTR pValueName, LPBYTE pValue);
<PERSON>ONG jRegEnumKey(LPCTSTR pSubKeyName, int nIndex, LPTSTR lpSubKey, int nSubkeyLen);

/*
#define _LOGINGATE_SERVER_REGISTRY	_T("Software\\LegendOfMir\\LoginGate")
jRegGetKey(_LOGINGATE_SERVER_REGISTRY, _T("RemoteIP"), (LPBYTE)&dwIP);
jRegSetKey(_LOGINGATE_SERVER_REGISTRY, _T("RemoteIP"), REG_DWORD, (LPBYTE)&dwIP, sizeof(DWORD));
*/